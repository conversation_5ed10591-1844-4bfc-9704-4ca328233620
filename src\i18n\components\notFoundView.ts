// NotFoundView 组件的国际化内容
export const notFoundView = {
  zh: {
    errorCode: '404',
    title: '页面走丢了',
    subtitle: '抱歉，您访问的页面不存在或已被移除',
    logoAlt: '好兽医AI助手',
    suggestions: {
      tryOtherWays: '尝试其他方式访问',
      contactStaff: '联系相关工作人员',
      checkUrl: '检查网址是否正确'
    }
  },
  
  en: {
    errorCode: '404',
    title: 'Page Not Found',
    subtitle: 'Sorry, the page you are looking for does not exist or has been removed',
    logoAlt: 'Vet AI Assistant',
    suggestions: {
      tryOtherWays: 'Try other ways to access',
      contactStaff: 'Contact relevant staff',
      checkUrl: 'Check if the URL is correct'
    }
  },
  
  ja: {
    errorCode: '404',
    title: 'ページが見つかりません',
    subtitle: '申し訳ございませんが、お探しのページは存在しないか削除されました',
    logoAlt: '獣医AIアシスタント',
    suggestions: {
      tryOtherWays: '他の方法でアクセスしてみる',
      contactStaff: '関連スタッフに連絡する',
      checkUrl: 'URLが正しいか確認する'
    }
  },
  
  ko: {
    errorCode: '404',
    title: '페이지를 찾을 수 없습니다',
    subtitle: '죄송합니다. 찾으시는 페이지가 존재하지 않거나 삭제되었습니다',
    logoAlt: '수의사 AI 어시스턴트',
    suggestions: {
      tryOtherWays: '다른 방법으로 접근해 보세요',
      contactStaff: '관련 직원에게 문의하세요',
      checkUrl: 'URL이 올바른지 확인하세요'
    }
  },
  
  th: {
    errorCode: '404',
    title: 'ไม่พบหน้าเว็บ',
    subtitle: 'ขออภัย หน้าเว็บที่คุณกำลังมองหาไม่มีอยู่หรือถูกลบไปแล้ว',
    logoAlt: 'ผู้ช่วย AI สำหรับสัตวแพทย์',
    suggestions: {
      tryOtherWays: 'ลองวิธีอื่นในการเข้าถึง',
      contactStaff: 'ติดต่อเจ้าหน้าที่ที่เกี่ยวข้อง',
      checkUrl: 'ตรวจสอบว่า URL ถูกต้องหรือไม่'
    }
  },
  
  ms: {
    errorCode: '404',
    title: 'Halaman Tidak Ditemui',
    subtitle: 'Maaf, halaman yang anda cari tidak wujud atau telah dibuang',
    logoAlt: 'Pembantu AI Veterinar',
    suggestions: {
      tryOtherWays: 'Cuba cara lain untuk mengakses',
      contactStaff: 'Hubungi kakitangan berkaitan',
      checkUrl: 'Semak sama ada URL betul'
    }
  },
  
  id: {
    errorCode: '404',
    title: 'Halaman Tidak Ditemukan',
    subtitle: 'Maaf, halaman yang Anda cari tidak ada atau telah dihapus',
    logoAlt: 'Asisten AI Veteriner',
    suggestions: {
      tryOtherWays: 'Coba cara lain untuk mengakses',
      contactStaff: 'Hubungi staf terkait',
      checkUrl: 'Periksa apakah URL sudah benar'
    }
  },
  
  vi: {
    errorCode: '404',
    title: 'Không Tìm Thấy Trang',
    subtitle: 'Xin lỗi, trang bạn đang tìm kiếm không tồn tại hoặc đã bị xóa',
    logoAlt: 'Trợ lý AI Thú y',
    suggestions: {
      tryOtherWays: 'Thử các cách khác để truy cập',
      contactStaff: 'Liên hệ nhân viên liên quan',
      checkUrl: 'Kiểm tra xem URL có đúng không'
    }
  }
};
