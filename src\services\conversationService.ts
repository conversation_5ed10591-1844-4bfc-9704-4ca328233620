import {
  CHAT_PAGE_SIZE,
  CONVERSATION_PAGE_SIZE,
  CONVERSATION_TITLE,
} from "@/constants/constant";
import { ConversationType } from "@/constants/enums";
import api from "@/utils/api";

/**
 * 会话信息接口
 */
export interface ConversationInfo {
  id: number;
  title: string;
  conversation_id: string;
  conversation_type: number;
  created_at: string;
  voice_medical_records_id?: number;
}

/**
 * 会话管理类
 */
export class ConversationService {
  /**
   * 创建会话信息
   *
   */
  static async createConversationFromApi(
    title?: string,
    conversation_type?: number,
    voiceMedicalRecordsId?: number
  ): Promise<any> {
    const response = await api.post("/api/conversations/", {
      title: title ?? CONVERSATION_TITLE,
      conversation_type: conversation_type ?? ConversationType.DIAGNOSIS,
      voice_medical_records_id: voiceMedicalRecordsId,
    });
    return response;
  }

  /**
   * 获取会话列表
   *
   */
  static async getConversationListFromApi(
    page: number = 1,
    conversationType?: number
  ): Promise<any> {
    const response = await api.get("/api/conversations/", {
      size: CONVERSATION_PAGE_SIZE,
      page: page,
      conversation_type: conversationType ?? ConversationType.DIAGNOSIS,
    });
    return response;
  }

  /**
   * 获取聊天历史
   * @param page 页码，从1开始
   * @param size 每页数量，默认10条
   */
  static async getChatHistoryFromApi(
    conversationId: string,
    page: number = 1
  ): Promise<any> {
    const response = await api.get<any>(
      `/api/conversations/${conversationId}/messages`,
      {
        order_by: "created_at",
        order_desc: true,
        size: CHAT_PAGE_SIZE,
        page,
      }
    );
    return response;
  }

  /**
   * 删除会话中所有聊天历史
   * @param conversationId 会话ID
   */
  static async deleteMessageListFromApi(conversationId: number): Promise<any> {
    const response = await api.delete<any>(
      `/api/conversations/${conversationId}/messages`
    );
    return response;
  }

  /**
   * 删除会话
   * @param id 会话ID
   */
  static async deleteConversationFromApi(id: number): Promise<any> {
    const response = await api.delete(`/api/conversations/${id}`, {
      showError: true,
      showSuccess: true,
    });
    return response;
  }

  /**
   * 重命名会话
   * @param id 会话ID
   * @param title 新标题
   */
  static async renameConversationFromApi(
    id: number,
    title: string
  ): Promise<any> {
    const response = await api.put(
      `/api/conversations/${id}`,
      { title },
      {
        showError: true,
        showSuccess: true,
      }
    );
    return response;
  }
}

export default ConversationService;
