<template>
  <svg 
    xmlns="http://www.w3.org/2000/svg" 
    width="1em" 
    height="1em" 
    fill="none" 
    viewBox="0 0 20 20" 
    :class="['icon', className]"
    :style="{ fontSize: iconSize }"
  >
    <rect 
      width="16.799" 
      height="14.822" 
      x="1.601" 
      y="2.589" 
      stroke="currentColor" 
      stroke-width="1.875" 
      rx="2.75"
    />
    <path 
      stroke="currentColor" 
      stroke-linecap="round" 
      stroke-width="1.875" 
      d="M9.069 3.412v13.175M4.402 7.177h1.866M4.402 10h1.866M4.402 12.823h1.866"
    />
  </svg>
</template>

<script lang="ts" setup>
interface Props {
  className?: string;
  iconSize?: string;
}

withDefaults(defineProps<Props>(), {
  className: '',
  iconSize: 'var(--icon-size, 1em)'
});
</script>

<style scoped>
.icon {
  display: inline-block;
  vertical-align: middle;
  color: currentColor;
  transition: all 0.3s ease;
}
</style>
