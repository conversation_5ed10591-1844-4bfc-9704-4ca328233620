@import "/src/styles/components/_chat-bubble-list.css";

/* VoiceView 特有样式 */
.bubble-header {
  width: 100%;
  margin-bottom: 0.4dvw;
}

.bubble-header-title {
  margin-block: 0 0.5dvw;
  padding-bottom: 0.3dvw;
  border-bottom: 1px solid var(--el-color-info-light-5);
}

.bubble-header-info {
  display: flex;
}

.bubble-header-info .info-content {
  width: 100%;
}

.bubble-header-info .info-logo {
  width: 3dvw;
  height: 3dvw;
}

.bubble-header-info .info-subtitle {
  margin-block: 0 0.3dvw;
}

.bubble-header-info .info-desc {
  font-size: 0.75dvw;
  color:var(--el-color-info-dark-2)
}

.bubble-list-container .el-bubble-list {
  height: 80dvh;
}

:deep(
  .bubble-list
    .el-bubble.el-bubble-start
    .el-bubble-content.el-bubble-content-filled
) {
  width: 100%;
  max-width: 100%;
}

:deep(.bubble-list .el-bubble-footer) {
  width: 100%;
  max-width: 100%;
}

:deep(.bubble-list .el-bubble-header) {
  max-width: 100%;
}

:deep(.bubble-list .el-bubble.el-bubble-start .el-bubble-avatar-placeholder) {
  display: none;
}

.footer-container {
  flex-direction: column;
}
