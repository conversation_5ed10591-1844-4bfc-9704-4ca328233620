<script setup lang="ts">
import type { ImageAttachment } from "@/services/chatService";
import { OcrService } from "@/services/ocrService";
import { type UploadFile, type UploadFiles, type UploadRequestOptions } from "element-plus";
import MarkdownIt from "markdown-it";
import { computed, onBeforeUnmount, ref, watch } from "vue";
import { useI18n } from "vue-i18n";

// 国际化
const { t } = useI18n();

// Props
interface Props {
  modelValue: boolean;
}

// Emits
interface Emits {
  (e: "update:modelValue", value: boolean): void;
  (e: "complete", files: ImageAttachment[]): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const imageAttachments = ref<ImageAttachment[]>([]);
  const errorImageAttachments = ref<UploadFile[]>([]);
const curFile = ref<ImageAttachment>();
const loadingUids = ref<number[]>([]);
const showPreview = ref(false)
const showPreviewImageBase64 = ref<string[]>([])

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit("update:modelValue", value),
});

const loading = computed((): boolean => {
  return !!(loadingUids.value && loadingUids.value?.length > 0);
});

const successFiles = computed(() => {
  return imageAttachments.value.filter((file) => file.status === "success");
});

const showFile = computed(() => {
  const errorFile = errorImageAttachments.value.length > 0 ?
    {
      id: errorImageAttachments.value[0].uid,
      uid: errorImageAttachments.value[0].uid,
      name: errorImageAttachments.value[0].name,
      status: "error",
      response: {
        ocrText: "暂无解析结果",
        base64: errorImageAttachments.value[0].url!
      }
    } : []
  return curFile.value || successFiles.value[0] || errorFile || null;
});

const handleUpload = async (options: UploadRequestOptions): Promise<any> => {
  const { file, onSuccess, onError } = options;
  let data: any = {};
  try {
    // 添加到loading列表
    loadingUids.value.push(file.uid);
    const base64 = await OcrService.fileToBase64(file);
    data.base64 = base64;
    const response = await OcrService.ocrText(base64, 'imaging_report');
    onSuccess(response.result);
    data.ocrText = response.result?.text;
  } catch (err: any) {
    onError(err);
  } finally {
    // 从loading列表中移除
    loadingUids.value = loadingUids.value.filter((uid) => uid !== file.uid);
    return data;
  }
};

const handleUploadError = async (_error: Error, uploadFile: UploadFile, _uploadFiles: UploadFiles): Promise<void> => {
  const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        if (typeof reader.result === 'string') {
          resolve(reader.result);
        } else {
          reject(new Error('无法转换文件为Base64'));
        }
      };
      reader.onerror = (error) => reject(error);
    });
  };
  try {
    if (uploadFile.raw) {
      const base64String = await fileToBase64(uploadFile.raw);
      errorImageAttachments.value = [
        ...errorImageAttachments.value,
        {
          ...uploadFile,
          url: base64String  // 添加base64Url属性存储Base64字符串
        }
      ];
    }
  } catch (error) {
    console.error('文件转换为Base64失败:', error);
  }
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  imageAttachments.value = [];
  errorImageAttachments.value = [];
  loadingUids.value = [];
};

// 完成上传
const handleComplete = () => {
  emit("complete", successFiles.value);
  handleClose();
};

// 选择文件显示OCR文本
const handleSelectFile = (file: ImageAttachment) => {
  curFile.value = file;
};

// 选择文件显示OCR文本
const handleSelectErrorFile = (file: UploadFile) => {
  curFile.value = {
    id: file.uid,
    uid: file.uid,
    name: file.name,
    status: "error",
    response: {
      ocrText: "暂无解析结果",
      base64: file.url!
    }
  };
};
// 预览图片（放大显示）
const handlePreviewImage = (file: ImageAttachment) => {
  const imageBase64 = file.response.base64;
  showPreviewImageBase64.value = [imageBase64]
  showPreview.value = true;
};

// 预览失败图片（放大显示）
const handlePreviewErrorImage = (file: UploadFile) => {
  const imageBase64 = file.url as string;
  showPreviewImageBase64.value = [imageBase64]
  showPreview.value = true;
};

const parsedOcrText = computed(() => {
  if (!showFile.value?.response?.ocrText) return null;
  const md = new MarkdownIt();
  return md.render(showFile.value.response.ocrText);
});

// 计算属性：上传状态文本
const uploadLoadingText = computed(() => {
  return t('uploadReport.upload.loading', { count: loadingUids.value.length.toString() });
});

const uploadNormalText = computed(() => {
  return {
    dragText: t('uploadReport.upload.dragText'),
    clickText: t('uploadReport.upload.clickText'),
    pasteText: t('uploadReport.upload.pasteText')
  };
});

// 选择文件显示OCR文本
const handleDeleteFile = (file: ImageAttachment) => {
  // 从ImageAttachments数组中删除指定的文件
  imageAttachments.value = imageAttachments.value.filter(
    (item) => item.uid !== file.uid
  );

  // 如果当前选中的文件就是被删除的文件，则清空curFile
  if (curFile.value?.uid === file.uid) {
    curFile.value = undefined;
  }
};

// 选择文件显示OCR文本
const handleDeleteErrorFile = (file: UploadFile) => {
  // 从ImageAttachments数组中删除指定的文件
  errorImageAttachments.value = errorImageAttachments.value.filter(
    (item) => item.uid !== file.uid
  );

  // 如果当前选中的文件就是被删除的文件，则清空curFile
  if (curFile.value?.uid === file.uid) {
    curFile.value = undefined;
  }
};

// 处理 Ctrl+V 粘贴上传
const genUid = () => Date.now() + Math.floor(Math.random() * 10000);

const handlePaste = async (e: ClipboardEvent) => {
  if (!e.clipboardData) {
    return;
  }
  const items = e.clipboardData.items;
  const files: File[] = [];
  // 检查 DataTransferItemList
  if (items && items.length > 0) {
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      if (item.kind === "file" && item.type.startsWith("image/")) {
        const file = item.getAsFile();
        if (file) {
          files.push(file);
        }
      }
    }
  }

  // 检查 FileList (备用方案)
  if (files.length === 0 && e.clipboardData.files && e.clipboardData.files.length > 0) {
    for (let i = 0; i < e.clipboardData.files.length; i++) {
      const file = e.clipboardData.files[i];
      if (file.type.startsWith("image/")) {
        files.push(file);
      }
    }
  }
  if (files.length === 0) {
    return;
  }
  e.preventDefault();
  e.stopPropagation();
  for (const file of files) {
    const uid = genUid();
    // 标记loading
    loadingUids.value.push(uid);
    try {
      const base64 = await OcrService.fileToBase64(file);
      const ocr = await OcrService.ocrText(base64, 'imaging_report');
      const attachment: ImageAttachment = {
        id: uid,
        uid,
        name: file.name || t('uploadReport.upload.pasteFileName', { timestamp: Date.now().toString() }),
        size: file.size,
        response: {
          base64,
          ocrText: ocr.result?.text,
        },
        raw: file,
        percentage: 100,
        status: "success",
      };
      imageAttachments.value = [...imageAttachments.value, attachment];

    } catch (err: any) {
      console.error(t('uploadReport.upload.processingError'), err);

      const attachment: ImageAttachment = {
        id: uid,
        uid,
        name: file.name || t('uploadReport.upload.pasteFileName', { timestamp: Date.now().toString() }),
        size: file.size,
        response: {
          base64: "",
        },
        raw: file,
        percentage: 0,
        status: "error",
        error: err?.message || String(err),
      };
      imageAttachments.value = [...imageAttachments.value, attachment];

    } finally {
      loadingUids.value = loadingUids.value.filter((id) => id !== uid);
    }
  }
};

// 仅在弹窗打开时监听粘贴
const pasteListener = (e: Event) => {
  handlePaste(e as ClipboardEvent);
};

watch(dialogVisible, (visible) => {
  if (visible) {
    window.addEventListener("paste", pasteListener);
    // 也在对话框元素上监听，确保能捕获到事件
    document.addEventListener("paste", pasteListener);
  } else {
    window.removeEventListener("paste", pasteListener);
    document.removeEventListener("paste", pasteListener);
  }
});

onBeforeUnmount(() => {
  window.removeEventListener("paste", pasteListener);
  document.removeEventListener("paste", pasteListener);
});

</script>

<template>
  <el-dialog v-model="dialogVisible" :title="t('uploadReport.title')" width="70%" :before-close="handleClose" top="3vh"
    style="margin-bottom: 0" center>
    <div class="upload-report-dialog">
      <div class="upload-section">
        <div class="upload-content ocr-text" v-html="parsedOcrText" v-if="successFiles && successFiles.length > 0 || errorImageAttachments && errorImageAttachments.length > 0">
        </div>
        <div v-else style="height: 50dvh; text-align: center">
          <img style="height: 100%; border-radius: 5dvw" src="/chat/no_upload_report_files.jpeg" />
        </div>
        <el-upload drag multiple v-model:file-list="imageAttachments" :http-request="handleUpload" :disabled="loading"
          :show-file-list="false" :on-error="handleUploadError">
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            <template v-if="loading">
              {{ uploadLoadingText }}
            </template>
            <template v-else>
              {{ uploadNormalText.dragText }} <em>{{ uploadNormalText.clickText }}</em> 或 <em>{{
                uploadNormalText.pasteText }}</em>
            </template>
          </div>
        </el-upload>
      </div>
      <div class="upload-slider" v-if="successFiles && successFiles.length > 0 || errorImageAttachments && errorImageAttachments.length > 0">
        <ul class="upload-file-list">
          <li v-for="file in successFiles" :key="file.id" class="upload-file-item"
            :class="{ active: curFile?.uid === file.uid }" @click="handleSelectFile(file)">
            <div class="upload-file-image-container">
              <el-image :src="file.response.base64" fit="cover" class="upload-file-image" />
              <div class="upload-file-image-options">
                <el-button class="expand-button" size="small" circle @click.stop="handlePreviewImage(file)">
                  <el-icon><zoom-in /></el-icon>
                </el-button>
                <el-button class="delete-button" size="small" circle @click.stop="handleDeleteFile(file)">
                  <el-icon>
                    <delete />
                  </el-icon>
                </el-button>
              </div>
            </div>
          </li>
          <li v-for="file in errorImageAttachments" :key="file.uid" class="upload-file-item error"
            :class="{ active: curFile?.uid === file.uid }" @click="handleSelectErrorFile(file)">
            <div class="upload-file-image-container">
              <el-image :src="file.url" fit="cover" class="upload-file-image" />
              <div class="upload-file-image-options">
                <el-button class="expand-button" size="small" circle @click.stop="handlePreviewErrorImage(file)">
                  <el-icon><zoom-in /></el-icon>
                </el-button>
                <el-button class="delete-button" size="small" circle @click.stop="handleDeleteErrorFile(file)">
                  <el-icon>
                    <delete />
                  </el-icon>
                </el-button>
              </div>
            </div>
            <el-text class="upload-file-title" truncated>
              解析失败
            </el-text>
          </li>
        </ul>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" class="cancel-button">{{ t('uploadReport.buttons.cancel') }}</el-button>
        <el-button type="primary" @click="handleComplete" class="complete-button" :loading="loading">
          {{ t('uploadReport.buttons.complete') }}
        </el-button>
      </div>
    </template>
    <el-image-viewer v-if="showPreview" hide-on-click-modal :url-list="showPreviewImageBase64" @close="showPreview = false" />
  </el-dialog>
</template>

<style scoped src="./UploadReport.css"></style>
