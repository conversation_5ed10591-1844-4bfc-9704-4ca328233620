/* 桌面端样式 (768px及以上) */
@media (min-width: 768px) {
  .login-content {
    height: 65dvh;
    border-radius: 1dvw;
    display: none;
  }

  /* 语言切换器样式 */
  .language-switcher {
    top: 1.5dvh;
    right: 1.5dvw;
  }

  .globe-icon {
    --icon-size: 24px;
  }

  /* Popover 内容样式 */
  .language-option {
    padding: 8px 16px;
  }

  .language-option .flag {
    margin-right: 12px;
    font-size: 16px;
  }

  .language-option .label {
    font-size: 14px;
  }

  /* 左侧内容区域 - 桌面端独有 */
  .login-content-left {
    flex: 1;
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(145deg, #87ceeb 0%, #6097fc 70%);
    height: 100%;
  }

  .left-content-container {
    width: 100%;
    max-width: 90%;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 1dvw;
  }

  .logo-section {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1dvw;
    position: relative;
    width: 100%;
    justify-content: space-evenly;
  }

  .centered-logo {
    width: 9dvw;
    height: auto;
    filter: drop-shadow(0 0 15px rgba(255, 255, 255, 0.4));
    z-index: 2;
  }

  .feature-icons-left,
  .feature-icons-right {
    display: flex;
    flex-direction: column;
    gap: 2dvw;
    align-items: center;
  }

  .floating-icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5dvw;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 400;
    text-align: center;
    animation: floatUpDown 3s ease-in-out infinite;
    --icon-size: 2dvw;
  }

  .floating-icon:nth-child(1) {
    animation-delay: 0s;
  }

  .floating-icon:nth-child(2) {
    animation-delay: 1.5s;
  }

  .feature-icons-right .floating-icon:nth-child(1) {
    animation-delay: 0.75s;
  }

  .feature-icons-right .floating-icon:nth-child(2) {
    animation-delay: 2.25s;
  }

  .floating-icon span {
    font-size: 0.9dvw;
    color: rgba(255, 255, 255, 0.7);
    text-shadow: 0 0 5px rgba(255, 255, 255, 0.2);
  }

  @keyframes floatUpDown {
    0%,
    100% {
      transform: translateY(0px);
    }

    50% {
      transform: translateY(-8px);
    }
  }

  .intro-section {
    margin-bottom: 1.5dvw;
  }

  .app-title {
    font-size: 2.2dvw;
    font-weight: 700;
    color: white;
    margin: 0 0 1dvw 0;
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.6);
    background: linear-gradient(135deg, #ffffff 0%, #e0f7ff 50%, #87ceeb 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: 0.1dvw;
  }

  .app-description {
    font-size: 1dvw;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
    margin: 0;
    font-weight: 400;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
    max-width: 85%;
    margin: 0 auto;
  }

  /* 右侧内容区域 */
  .login-content-right {
    gap: 1.2dvw;
    padding-inline: 1dvw;
  }

  .login-content-right-title {
    font-size: 1.8dvw;
    letter-spacing: 0.15dvw;
    margin-bottom: 2dvw;
  }

  .login-phone-group {
    width: 75%;
  }

  .login-phone-select {
    width: 25%;
  }

  .login-phone-input {
    width: 75%;
    padding-block: 0.2dvw;
  }

  .login-phone-group :deep(.el-input__inner) {
    font-weight: 500;
    font-size: 0.8dvw;
  }

  .login-phone-area-place {
    font-size: 0.7dvw;
  }

  .login-captcha-group {
    width: 75%;
  }

  .login-captcha-input {
    width: 65%;
    padding-block: 0.2dvw;
  }

  .login-captcha-group :deep(.el-input__inner) {
    font-weight: 500;
    font-size: 0.8dvw;
  }

  .login-captcha-button {
    width: 35%;
    font-size: 0.7dvw;
  }

  .login-button {
    width: 65%;
    height: 4.5dvh;
    border-radius: 30px;
    font-size: 1.1dvw;
  }

  .checkbox-group {
    gap: 0.6dvw;
    margin: 0.8dvw 0;
    width: 80%;
  }

  .policy-checkbox {
    font-size: 0.8dvw;
    font-weight: 400;
    letter-spacing: 0.02dvw;
  }

  .policy-checkbox :deep(.el-checkbox__label) {
    font-size: 0.8dvw;
    font-weight: 400;
    letter-spacing: 0.02dvw;
  }
}
