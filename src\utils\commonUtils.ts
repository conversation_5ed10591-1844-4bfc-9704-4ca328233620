import i18n from "@/i18n";
import { ElMessage } from "element-plus";

/**
 * CommonUtils 工具类
 */
export class CommonUtils {
  /**
   * 复制
   */
  static copyString = async (copyString: string) => {
    try {
      // 优先使用 Clipboard API（需要 HTTPS 或 localhost）
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(copyString);
        ElMessage.success(i18n.global.t("bubbleList.copySuccess"));
        return;
      }

      // 降级方案：使用传统的 document.execCommand（兼容 HTTP）
      const textArea = document.createElement("textarea");
      textArea.value = copyString;
      textArea.style.position = "fixed";
      textArea.style.left = "-999999px";
      textArea.style.top = "-999999px";
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();

      const successful = document.execCommand("copy");
      document.body.removeChild(textArea);

      if (successful) {
        ElMessage.success(i18n.global.t("bubbleList.copySuccess"));
      } else {
        throw new Error("execCommand copy failed");
      }
    } catch (error) {
      ElMessage.error(i18n.global.t("bubbleList.copyFailed"));
      console.error("复制失败:", error);
    }
  };
}
