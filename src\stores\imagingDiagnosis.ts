import ImagingDiagnosisService, {
  type GetXRayRecordsResponse,
  type XRayRecords,
} from "@/services/imagingDiagnosisService";
import type { UploadUserFile } from "element-plus";
import { defineStore } from "pinia";
import { ref } from "vue";

export const useImagingDiagnosisStore = defineStore("imagingDiagnosis", () => {
  const imageFiles = ref<UploadUserFile[]>([]);
  const analyzeResult = ref();

  // 影像诊断分析
  const imageAnalyze = async (images: { url: string }[]): Promise<any> => {
    return await ImagingDiagnosisService.imageAnalyzeFromApi(images);
  };

  // 更新或新增影像报告
  const saveOrUpdateXRayRecords = async (
    xRayRecords: XRayRecords
  ): Promise<any> => {
    return await ImagingDiagnosisService.saveOrUpdateXRayRecordsFromApi(
      xRayRecords
    );
  };

  // 查询影像报告
  const getXRayRecords = async (
    id: number
  ): Promise<GetXRayRecordsResponse> => {
    return await ImagingDiagnosisService.getXRayRecordsFromApi(id);
  };
  return {
    imageFiles,
    analyzeResult,
    imageAnalyze,
    saveOrUpdateXRayRecords,
    getXRayRecords,
  };
});
