.imaging-diagnosis {
  display: flex;
  flex-direction: column;
  gap: 1dvw;
  height: 100%;
}

.content-container {
  background-color: white;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 0.8dvw;
  display: flex;
  flex-direction: column;
  flex: 1;
  box-shadow: 3px 3px 7px 2px rgba(0, 0, 0, 0.2);
}

/* 无图片站位样式 */
.no-image-show-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.no-image-show-img {
  height: 11dvw;
}

.no-image-show-text {
  display: flex;
  font-size: 0.75dvw;
  color: var(--el-text-color-secondary);
}

.no-image-show-text .icon {
  font-size: 1dvw;
  color: var(--el-text-color-secondary);
  margin-right: 0.6dvw;
}

/* 有图片样式 */
.no-main-image {
  width: 100%;
  height: 100%;
  background: var(--el-color-info-light-9);
}

.upload-file-image-options {
  position: absolute;
  top: 2%;
  right: 2%;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.upload-file-image-options:hover {
  opacity: 1;
}

:deep(.left-container:hover .upload-file-image-options) {
  opacity: 1;
}

.image-show-card {
  flex: 1;
  display: grid;
  grid-template-columns: 70% 30%;
}

.left-container,
.right-container {
  position: relative;
}

.left-container {
  margin: 0.8dvw;
}

.right-container {
  margin: 0.8dvw 0.8dvw 0.8dvw 0;
}

.left-container .main-image {
  width: 100%;
  height: 100%;
  position: absolute;
  border-radius: 0.2dvw;
}

:deep(.left-container:hover .upload-file-image-options) {
  opacity: 1;
}

.right-container {
  display: flex;
  flex-direction: column;
}

.right-container .secondary-image {
  width: 100%;
  height: 32%;
  position: absolute;
  border-radius: 0.2dvw;
}

.secondary-image.num-1:hover ~ .upload-file-image-options.num-1 {
  opacity: 1;
}

.secondary-image.num-2:hover ~ .upload-file-image-options.num-2 {
  opacity: 1;
}

.secondary-image.num-3:hover ~ .upload-file-image-options.num-3 {
  opacity: 1;
}

.right-container .secondary-image.num-1 {
  top: 0%;
}
.right-container .secondary-image.num-2 {
  top: 34%;
}
.right-container .secondary-image.num-3 {
  top: 68%;
}

.right-container .upload-file-image-options.num-1 {
  top: 2%;
}
.right-container .upload-file-image-options.num-2 {
  top: 36%;
}
.right-container .upload-file-image-options.num-3 {
  top: 70%;
}

.image-upload-card {
  flex: 0;
}

:deep(.upload-dragger .el-upload-dragger) {
  border-radius: 0 0 0.8dvw 0.8dvw;
}

/* 底部上传区样式 */
.footer-container {
  background: white;
  padding: 0dvw 1.5dvw 1dvw;
  border-radius: 0.8dvw;
  box-shadow: 3px 3px 7px 2px rgba(0, 0, 0, 0.2);
}

.footer-container .title {
  font-size: 0.9dvw;
  margin-bottom: 0.6dvw;
}

.footer-container .text-container {
  font-size: 0.75dvw;
  padding-block: 0.2dvw 0.8dvw;
}

.text-container .text-area {
  font-size: 0.75dvw;
}

:deep(.text-area .el-textarea__inner) {
  border: none;
  resize: none;
  box-shadow: none;
  background: transparent;
}

:deep(.text-area .el-textarea__inner:focus) {
  border: none;
  box-shadow: none;
}

.footer-container .button-container {
  display: flex;
  justify-content: right;
}
