@import "/src/styles/components/_ocr-text.css";

.upload-report-dialog {
  height: 75dvh;
  display: flex;
  gap: 0.6vw;
}

.upload-section {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  flex: 5;
  gap: 0.6vw;
  width: 0%;
}

.upload-content {
  overflow: auto;
  background: linear-gradient(
    145deg,
    rgba(135, 206, 235, 0.08) 0%,
    rgba(96, 151, 252, 0.05) 100%
  );
  backdrop-filter: blur(10px);
  border: 1px solid rgba(135, 206, 235, 0.2);
  border-radius: 0.5dvw;
  padding: 1dvw;
}

.upload-slider {
  overflow: auto;
  height: 100%;
  flex: 1;
}

.upload-file-list {
  list-style: none;
  padding: 12px;
  margin: 0;
  background: linear-gradient(
    145deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(135, 206, 235, 0.05) 100%
  );
  border-radius: 8px;
  border: 1px solid rgba(135, 206, 235, 0.2);
  backdrop-filter: blur(5px);
}

.upload-file-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-bottom: 16px;
  padding: 8px;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(135, 206, 235, 0.03) 100%
  );
  border-radius: 6px;
  border: 1px solid rgba(135, 206, 235, 0.2);
  transition: all 0.3s ease;
  cursor: pointer;
  backdrop-filter: blur(5px);
}

.upload-file-item.error {
  border: 1px solid rgba(235, 135, 135, 0.4);
}

.upload-file-item:last-child {
  margin-bottom: 0;
}

.upload-file-item:hover {
  border-color: rgba(135, 206, 235, 0.4);
  box-shadow: 0 2px 4px rgba(96, 151, 252, 0.15);
}

.upload-file-item.error:hover {
  border: 1px solid rgba(235, 135, 135, 0.6);
  box-shadow: 0 2px 4px rgba(252, 96, 96, 0.15);
}

.upload-file-item.active {
  border-color: #6097fc;
  box-shadow: 0 2px 8px rgba(96, 151, 252, 0.25);
  background: linear-gradient(
    135deg,
    rgba(135, 206, 235, 0.1) 0%,
    rgba(96, 151, 252, 0.05) 100%
  );
}

.upload-file-item.error.active {
  border-color: #fc6060;
  box-shadow: 0 2px 8px rgba(252, 96, 96, 0.25);
  background: linear-gradient(
    135deg,
    rgba(235, 135, 135, 0.1) 0%,
    rgba(252, 96, 96, 0.05) 100%
  );
}

.upload-file-image-container {
  position: relative;
  width: 100%;
}

.upload-file-image-options {
  opacity: 0;
  position: absolute;
  top: 4px;
  right: 4px;
  transition: opacity 0.3s ease;
}

.upload-file-item:hover .upload-file-image-options {
  opacity: 1;
}

.expand-button {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(135, 206, 235, 0.1) 100%
  );
  border: 1px solid rgba(135, 206, 235, 0.3);
  backdrop-filter: blur(5px);
}

.expand-button:hover {
  background: linear-gradient(135deg, #6097fc 0%, #87ceeb 100%);
  border-color: #6097fc;
  color: white;
  box-shadow: 0 2px 6px rgba(96, 151, 252, 0.2);
}

.delete-button {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(220, 53, 69, 0.1) 100%
  );
  border: 1px solid rgba(135, 206, 235, 0.3);
  backdrop-filter: blur(5px);
}

.delete-button:hover {
  background: linear-gradient(135deg, #dc3545 0%, #f56565 100%);
  border-color: #dc3545;
  color: white;
  box-shadow: 0 2px 6px rgba(220, 53, 69, 0.2);
}

.upload-file-title {
  text-align: center;
  width: 6dvw;
  display: block;
  color: #ff5465;
  font-size: 0.65dvw;
}

.upload-file-image {
  height: 13dvh;
  width: 100%;
  border-radius: 4px;
  overflow: hidden;
}

.dialog-footer {
  padding-top: 1dvw;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}
