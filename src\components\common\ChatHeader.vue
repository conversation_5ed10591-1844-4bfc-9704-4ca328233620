<script lang="ts" setup>
import { LANGUAGE_OPTIONS } from "@/constants/constant";
import { useCommonStore } from "@/stores/common";
import { useSliderStore } from "@/stores/slider";
import { storeToRefs } from "pinia";
import { onMounted } from "vue";
import { useI18n } from "vue-i18n";
import GlobeIcon from "../icons/GlobeIcon.vue";

// 全局的Pina库
const sliderStore = useSliderStore();
const commonStore = useCommonStore();
const { t } = useI18n();

// 全局的响应数据
const { curFunction } = storeToRefs(sliderStore);
const { currentLanguageInfo, isMobile } = storeToRefs(commonStore);

// 组件挂载时设置浏览器默认语言
onMounted(() => {
  commonStore.initializeLanguage();
});
</script>

<template>
  <div class="header-container">
    <div class="header-left"></div>
    <div class="header-center">
      {{ t(curFunction.title) }}
    </div>
    <div class="header-right">
      <el-popover placement="bottom-end" :width="isMobile ? '50dvw' : '12dvw'">
        <template #reference>
          <el-button :link="isMobile" round >
            <div class="language-button-content">
              <GlobeIcon class="globe-icon" :class="{ isMobile: isMobile }" />
              <span v-if="!isMobile" class="language-text">{{ currentLanguageInfo.label }}</span>
            </div>
          </el-button>
        </template>
        <template #default>
          <div
            v-for="option in LANGUAGE_OPTIONS"
            :key="option.value"
            class="language-option"
            :class="{
              active: option.value === commonStore.currentLanguageInfo.value,
            }"
            @click="commonStore.handleLanguageChange(option.value)"
          >
            <span class="flag" :class="['fi', `fi-${option.flag}`]"></span>
            <span class="label">{{ option.label }}</span>
          </div>
        </template>
      </el-popover>
    </div>
  </div>
</template>

<style scoped src="./ChatHeader.css"></style>
