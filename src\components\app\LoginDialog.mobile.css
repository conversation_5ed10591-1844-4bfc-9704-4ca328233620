/* 移动端样式 (767px及以下) */
@media (max-width: 767px) {
.login-content {
  height: 60dvh;
  border-radius: 4dvw;
}

/* 语言切换器样式 */
.language-switcher {
  top: 2dvh;
  right: 3dvw;
}

.globe-icon {
  --icon-size: 8dvw;
}

/* Popover 内容样式 */
.language-option {
  padding: 2dvw 4dvw;
}

.language-option .flag {
  margin-right: 3dvw;
  font-size: 5dvw;
}

.language-option .label {
  font-size: 3.5dvw;
}

/* 右侧内容区域 */
.login-content-right {
  gap: 5dvw;
  padding-inline: 2dvw;
}

.login-content-right-title {
  font-size: 6dvw;
  font-weight: bold;
  letter-spacing: 0.2dvw;
  margin-bottom: 2dvw;
}

.login-phone-group {
  width: 80%;
}

.login-phone-select {
  width: 35%;
}

.login-phone-input {
  width: 75%;
  padding-block: 1.2dvw;
}

.login-phone-group :deep(.el-input__inner) {
  font-size: 3.4dvw;
}

.login-phone-area-place {
  font-size: 3dvw;
}

.login-captcha-group {
  width: 80%;
}

.login-captcha-input {
  padding-block: 1.2dvw;
}

.login-captcha-group :deep(.el-input__inner) {
  font-size: 3.4dvw;
}

.login-captcha-button {
  width: 45%;
  font-size: 3.2dvw;
}

/* 登录按钮样式 - 移动端 */
.login-button {
  width: 65%;
  height: 6dvh;
  border-radius: 8dvw;
  font-size: 5.5dvw;
  font-weight: bold;
}

/* 复选框组样式 - 移动端 */
.checkbox-group {
  gap: 1dvw;
  margin: 1dvw 0;
  width: 80%;
}

.policy-checkbox :deep(.el-checkbox__label) {
  font-size: 2.6dvw;
  letter-spacing: 0.1dvw;
}
}