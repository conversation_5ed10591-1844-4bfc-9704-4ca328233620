.dislike-feedback-container {
  display: flex;
  flex-direction: column;
  padding-inline: 6px;
}

.dislike-feedback-category-list,
.dislike-feedback-desc-input,
.dislike-feedback-helpful-input {
  margin-inline: 16px;
}

.dislike-feedback-category-list {
  margin-block: 16px 24px;
}

.dislike-feedback-desc {
  margin-bottom: 12px;
}

.dislike-feedback-title {
  color: #363636;
  font-size: 1rem;
  font-weight: bold;
  margin-bottom: 12px;
  letter-spacing: 0.4px;
}

.dislike-feedback-category-list {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.dislike-feedback-category-item {
  border: 2px solid var(--el-color-info-light-8);
  border-radius: 12px;
  padding-block: 12px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
}

.dislike-feedback-category-item:hover {
  background: linear-gradient(135deg, rgba(128, 128, 128, 0.15) 0%, rgba(169, 169, 169, 0.1) 100%);
  border-color: var(--el-color-info-light-5);
  box-shadow: 0 2px 8px rgba(128, 128, 128, 0.15);
  transform: translateY(-1px);
}

.dislike-feedback-category-item.active {
  background: linear-gradient(135deg, rgba(128, 128, 128, 0.25) 0%, rgba(169, 169, 169, 0.15) 100%);
  border-color: var(--el-color-info-light-3);
  box-shadow: 0 3px 12px rgba(128, 128, 128, 0.25);
  transform: translateY(-1px);
}

.dislike-feedback-category-item.active:hover {
  background: linear-gradient(135deg, rgba(128, 128, 128, 0.3) 0%, rgba(169, 169, 169, 0.2) 100%);
  border-color: var(--el-color-info);
  box-shadow: 0 4px 16px rgba(128, 128, 128, 0.3);
}

:deep(.dislike-feedback-helpful-input-textarea textarea) {
  border-radius: 12px;
  border: 1px solid var(--el-color-info-light-9);
}


.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 12px;
  padding-inline: 36px;
  margin-top: 24px;
}

.dialog-footer-button-cancel {
  padding-block: 24px;
  width: 30%;
  border-radius: 12px;
  font-size: 16px;
  letter-spacing: 1px;
}

.dialog-footer-button-submit {
  padding-block: 24px;
  width: 70%;
  border-radius: 12px;
  font-size: 16px;
  letter-spacing: 1px;
}