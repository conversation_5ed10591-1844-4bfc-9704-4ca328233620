/* 移动端样式 */
@import "./ChatHeader.mobile.css";

/* 桌面端样式 */
@import "./ChatHeader.desktop.css";

/* 基础样式 */
.header-container {
  height: 6dvh;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  align-items: center;
}

.header-center {
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin: auto;
  height: 6dvh;
}

.header-right {
  display: flex;
  justify-content: right;
  align-items: center;
}

/* 语言切换器基础样式 */
.language-option {
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border: none;
}

.language-option:hover {
  background-color: #f3f4f6;
}

.language-option.active {
  background-color: #eff6ff;
  color: #2563eb;
}

.language-option .flag {
  font-weight: 500;
}

.language-option .label {
  font-weight: 500;
  color: inherit;
}
