<script lang="ts" setup>
import CollapseIcon from "@/components/icons/CollapseIcon.vue";
import CopyIcon from "@/components/icons/CopyIcon.vue";
import DislikeFilledIcon from "@/components/icons/DislikeFilledIcon.vue";
import DislikeIcon from "@/components/icons/DislikeIcon.vue";
import ExpandIcon from "@/components/icons/ExpandIcon.vue";
import LikeFilledIcon from "@/components/icons/LikeFilledIcon.vue";
import LikeIcon from "@/components/icons/LikeIcon.vue";
import { CHAT_PAGE_SIZE } from "@/constants/constant";
import { useChatStore } from "@/stores/chat";
import { useSliderStore } from "@/stores/slider";
import { CommonUtils } from '@/utils/commonUtils';
import { ElMessage } from "element-plus";
import { throttle } from "lodash-es";
import { storeToRefs } from "pinia";
import { computed, nextTick, ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import Dislike from "./Dislike.vue";

// 国际化
const { t } = useI18n();

// 全局的Pina库
const chatStore = useChatStore();
const sliderStore = useSliderStore();

// 全局的响应数据
const { messageList, messagePagination, messageLoading, isAiTyping } =
  storeToRefs(chatStore);
const { curConversation } = storeToRefs(sliderStore);

//本组件的响应数据
const collapsedThinking = ref<Record<number, boolean>>({});
const showDislikeFeedbackDialog = ref<boolean>(false);
const currentDislikeMessageId = ref<number | null>(null);
const bubbleListRef = ref();
const bubbleList = computed(() => {
  return messageList.value.map((item) => {
    return {
      ...item,
      key: item.id,
      role: item.type,
      placement: item.type === "ai" ? "start" : "end",
      content: item.content,
      thinking: item.thinking || "",
      loading: item.thinkLoading,
      finished: !item.loading,
      isMarkdown: item.type === "ai",
      avatar: item.type === "ai" ? "logo.png" : "",
      avatarSize: "48px",
    };
  });
});
const hasMoreMessages = computed(() => {
  const page = messagePagination.value.page;
  const pages = messagePagination.value.pages;
  return page && pages ? page < pages : true;
});

//本组件的方法
const toggleThinking = (messageId: number) => {
  collapsedThinking.value[messageId] = !collapsedThinking.value[messageId];
};
const likeMessage = async (messageId: number) => {
  await chatStore.likeMessage(messageId);
  ElMessage.success(t('bubbleList.likeSuccess'));
};
const unlikeMessage = async (messageId: number) => {
  await chatStore.cancelLikeMessage(messageId);
  ElMessage.success(t('bubbleList.unlikeSuccess'));
};

const undislikeMessage = async (messageId: number) => {
  await chatStore.cancelDislikeMessage(messageId);
  ElMessage.success(t('bubbleList.undislikeSuccess'));
};

const handleShowDislikeFeedback = (messageId: number) => {
  currentDislikeMessageId.value = messageId;
  showDislikeFeedbackDialog.value = true;
};

const handleCloseDislikeFeedback = () => {
  currentDislikeMessageId.value = null;
  showDislikeFeedbackDialog.value = false;
};

const loadMoreMessages = throttle(
  async () => {
    if (!curConversation.value?.conversation_id) return;
    if (!bubbleListRef.value) return;
    const curPage = chatStore.messagePagination.page + 1;
    await chatStore.loadMoreMessageList(
      curConversation.value.conversation_id,
      curPage
    );
    // 等待DOM更新后恢复滚动位置
    await nextTick();
    bubbleListRef.value.scrollToBubble(CHAT_PAGE_SIZE * (curPage - 1))
  },
  2000,
  {
    leading: false,
    trailing: true,
  }
);

const bubbleListScroll = (event: Event) => {
  const target = event.target as HTMLElement;
  if (
    target?.scrollTop === 0 &&
    hasMoreMessages.value &&
    !messageLoading.value
  ) {
    loadMoreMessages();
  }
};

// 当有新消息时，自动滚动到底部
watch(
  () => isAiTyping.value,
  async (typing) => {
    if (typing) {
      await nextTick();
      bubbleListRef.value?.scrollToBottom?.();
    }
  }
);

// 会话切换时，滚动到底部
watch(
  () => curConversation.value?.conversation_id,
  async () => {
    await nextTick();
    bubbleListRef.value?.scrollToBottom?.();
  }
);
</script>

<template>
  <div class="bubble-list-container" :class="{ 'have-more-message': hasMoreMessages }">
    <BubbleList ref="bubbleListRef" :list="bubbleList" max-height="90dvh" class="bubble-list" @scroll="bubbleListScroll"
      :backButtonPosition="{ bottom: '20dvh', left: 'calc(50% - 19px)' }">
      <template #header="{ item }">
        <el-card v-if="item.thinking" class="thinking-container"
          :class="{ 'thinking-collapsed': collapsedThinking[item.id] }" header-class="thinking-header"
          body-class="thinking-body" shadow="never">
          <template #header>
            <div class="thinking-header-content">
              <span style="padding-right: 24px">{{
                item.thinkingLoading ? t('bubbleList.thinking.loading') : t('bubbleList.thinking.completed')
              }}</span>
              <el-button v-if="!item.loading" size="small" class="thinking-collapse-button"
                @click="toggleThinking(item.id)">
                <ExpandIcon v-if="collapsedThinking[item.id]" />
                <CollapseIcon v-else />
              </el-button>
            </div>
          </template>
          <transition name="thinking-collapse">
            <div v-show="!collapsedThinking[item.id]" class="thinking-content"
              :class="{ 'thinking-loading': item.thinkingLoading }">
              {{
                item.thinking || (item.thinkingLoading ? t('bubbleList.thinking.loading') : "")
              }}
            </div>
          </transition>
        </el-card>
      </template>
      <template #content="{ item }">
        <Typewriter class="bubble-content ocr-text" :content="item.content" :is-markdown="true" />
      </template>
      <template #footer="{ item }">
        <div class="footer-container" v-if="item.finished">
          <div class="footer-buttons" v-if="item.role === 'ai'
            && item.content
            && typeof item.id === 'number'
            && Number.isInteger(item.id)">
            <el-tooltip :content="t('bubbleList.copy')" placement="top" :show-after="100">
              <el-button size="small" text @click="CommonUtils.copyString(item.content)"
                class="footer-button copy-button">
                <CopyIcon />
              </el-button>
            </el-tooltip>
            <el-tooltip v-if="!item.isLike" :content="t('bubbleList.like')" placement="top" :show-after="100">
              <el-button size="small" text @click="likeMessage(item.id)" class="footer-button like-button">
                <LikeIcon />
              </el-button>
            </el-tooltip>
            <el-tooltip v-else :content="t('bubbleList.unlike')" placement="top" :show-after="100">
              <el-button size="small" text @click="unlikeMessage(item.id)" class="footer-button like-button liked">
                <LikeFilledIcon />
              </el-button>
            </el-tooltip>
            <el-tooltip v-if="!item.isDislike" :content="t('bubbleList.dislike')" placement="top" :show-after="100">
              <el-button size="small" text @click="handleShowDislikeFeedback(item.id)"
                class="footer-button dislike-button">
                <DislikeIcon />
              </el-button>
            </el-tooltip>
            <el-tooltip v-else :content="t('bubbleList.undislike')" placement="top" :show-after="100">
              <el-button size="small" text @click="undislikeMessage(item.id)"
                class="footer-button dislike-button disliked">
                <DislikeFilledIcon />
              </el-button>
            </el-tooltip>
          </div>
          <div class="footer-remarks" v-if="item.role === 'ai'">
            {{ t('bubbleList.disclaimer.chat') }}
          </div>
        </div>
      </template>
    </BubbleList>
    <!-- 不喜欢反馈对话框 -->
    <Dislike v-model:visible="showDislikeFeedbackDialog" :message-id="currentDislikeMessageId"
      @close="handleCloseDislikeFeedback" />
  </div>
</template>
<style scoped src="./BubbleList.css"></style>
