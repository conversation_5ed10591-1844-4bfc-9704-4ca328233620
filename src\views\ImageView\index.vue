<script lang="ts" setup>
import ChatHeader from "@/components/common/ChatHeader.vue";
import WelcomeLogo from '@/components/common/WelcomeLogo.vue';
import { ConversationType } from "@/constants/enums";
import ChatService, { type ChatMessage } from "@/services/chatService";
import { useChatStore } from "@/stores/chat";
import { useConversationStore } from "@/stores/conversation";
import { useSliderStore } from "@/stores/slider";
import { ElMessage } from "element-plus";
import { storeToRefs } from "pinia";
import { computed } from "vue";
import { useI18n } from "vue-i18n";
import BubbleList from "./components/BubbleList.vue";
import ChatInput from "./components/ChatInput.vue";
import ImagingDiagnosis from "./components/ImagingDiagnosis.vue";

// 国际化
const { t } = useI18n();

// 全局Pina库
const chatStore = useChatStore();
const sliderStore = useSliderStore();
const conversationStore = useConversationStore();

// 全局响应数据
const { messageList, isAiTyping } = storeToRefs(chatStore);
const { curConversation } = storeToRefs(sliderStore);

// 全局方法
const { addConversation } = conversationStore;

// 本组件的响应数据
const showBubbleList = computed(() => {
  return curConversation.value && messageList.value.length > 0
})

// 本组件的方法
function addErrorMessage(msg: string = "输出已中断，请重新发送消息"): void {
  const lastAiMessage = messageList.value
    .slice()
    .reverse()
    .find((msg) => msg.type === "ai");
  if (lastAiMessage) {
    lastAiMessage.loading = false;
    lastAiMessage.thinkLoading = false;
    if (!lastAiMessage.content) {
      lastAiMessage.content = msg;
    }
  }
}
const addMessage = (
  messageId: number,
  message: string,
  type: "user" | "ai" = "user"
): ChatMessage => {
  const newMessage: ChatMessage = {
    id: messageId,
    type,
    content: message,
    timestamp: Date.now(),
    loading: type === "ai",
    thinkLoading: type === "ai",
  };
  messageList.value.push(newMessage);
  return newMessage;
};
const updateMessage = (
  messageId: number,
  content: string,
  thinking?: string,
  finished?: boolean,
  thinkFinished?: boolean
): void => {
  const messageIndex = messageList.value.findIndex(
    (msg) => msg.id === messageId
  );
  if (messageIndex !== -1) {
    const message = messageList.value[messageIndex];

    const updates: Partial<typeof message> = {};

    if (content !== message.content) {
      updates.content = content;
    }
    if (thinking !== undefined && thinking !== message.thinking) {
      updates.thinking = thinking;
    }
    if (finished !== undefined && message.loading !== !finished) {
      updates.loading = !finished;
    }
    if (
      thinkFinished !== undefined &&
      message.thinkLoading !== !thinkFinished
    ) {
      updates.thinkLoading = !thinkFinished;
    }

    if (Object.keys(updates).length > 0) {
      Object.assign(message, updates);
    }
  }
};
const handleSendMessage = async (
  messageContent?: string
): Promise<void> => {
  const userMessage = messageContent || "";
  let curConversationId: string;

  if (!userMessage.trim()) {
    ElMessage.warning(t("systemMessages.enterMessageContent"));
    return;
  }
  if (isAiTyping.value) {
    ElMessage.warning(t("systemMessages.aiReplying"));
    return;
  }

  if (!!curConversation.value) {
    curConversationId = curConversation.value.conversation_id;
  } else {
    const newConversation = await addConversation(
      userMessage.slice(0, 20),
      ConversationType.IMAGE
    );
    curConversationId = newConversation.conversation_id;
    curConversation.value = newConversation;
  }
  addMessage(Date.now() + Math.random(), userMessage, "user");
  isAiTyping.value = true;
  try {
    await ChatService.sendMessageStream(
      {
        message: userMessage,
        conversationId: curConversationId,
      },
      (chunk) => {
        // 更新消息内容
        updateMessage(
          chunk.messageId,
          chunk.content,
          chunk.thinking,
          chunk.finished,
          chunk.thinkFinished
        );
      },
      (messageId) => {
        addMessage(messageId, "", "ai");
      }
    );
  } catch (error) {
    addErrorMessage(t("systemMessages.cannotReplyNow"));
  } finally {
    isAiTyping.value = false;
  }
};
</script>

<template>
  <ChatHeader />
  <div class="page-container">
    <div class="page-left">
      <ImagingDiagnosis />
    </div>
    <div class="page-right" :class="{ welcome: !showBubbleList }">
      <h4 v-if="showBubbleList" class="page-right-title">AI 辅助问诊</h4>
      <BubbleList v-if="showBubbleList" />
      <WelcomeLogo v-else />
      <ChatInput :handle-send-message="handleSendMessage" :is-ai-typing="isAiTyping"
        :class="{ 'fly-chat-input': showBubbleList }" />
    </div>
  </div>
</template>
<style scoped src="./index.css"></style>
