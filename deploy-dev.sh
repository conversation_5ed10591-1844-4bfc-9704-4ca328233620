#!/bin/bash

# 开发环境部署脚本
# 用途：拉取test分支代码并构建开发环境

set -e  # 遇到错误立即退出

echo "========================================="
echo "开始执行开发环境部署..."
echo "========================================="

# 获取当前时间
DEPLOY_TIME=$(date '+%Y-%m-%d %H:%M:%S')
echo "部署时间: $DEPLOY_TIME"

# 1. 拉取test分支代码
echo ""
echo "步骤 1/3: 拉取test分支最新代码..."
echo "-----------------------------------------"

# 检查当前分支
CURRENT_BRANCH=$(git branch --show-current)
echo "当前分支: $CURRENT_BRANCH"

# 切换到test分支并拉取最新代码
if [ "$CURRENT_BRANCH" != "test" ]; then
    echo "切换到test分支..."
    git checkout test
fi

echo "拉取最新代码..."
git pull origin test

echo "✅ 代码拉取完成"

# 2. 安装依赖（如果需要）
echo ""
echo "步骤 2/3: 检查并安装依赖..."
echo "-----------------------------------------"

if [ ! -d "node_modules" ] || [ ! -f "node_modules/.package-lock.json" ]; then
    echo "安装项目依赖..."
    npm install
    echo "✅ 依赖安装完成"
else
    echo "✅ 依赖已存在，跳过安装"
fi

# 3. 构建项目
echo ""
echo "步骤 3/3: 构建开发环境..."
echo "-----------------------------------------"

# 删除旧的dist目录（如果存在）
if [ -d "dist" ]; then
    echo "删除旧的dist目录..."
    rm -rf dist
fi

echo "开始构建..."
npm run build:dev

# 检查构建是否成功
if [ -d "dist" ]; then
    echo "✅ 构建完成，dist目录已生成"
else
    echo "❌ 构建失败，dist目录未生成"
    exit 1
fi

# 显示构建结果
DIST_SIZE=$(du -sh dist | cut -f1)
echo "构建产物大小: $DIST_SIZE"

echo ""
echo "========================================="
echo "🎉 开发环境部署成功！"
echo "========================================="
echo "部署时间: $DEPLOY_TIME"
echo "完成时间: $(date '+%Y-%m-%d %H:%M:%S')"
echo "分支: test"
echo "构建模式: development"
echo "输出目录: ./dist"
echo "构建大小: $DIST_SIZE"
echo "========================================="
