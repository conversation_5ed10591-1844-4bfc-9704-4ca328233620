<script lang="ts" setup>
import AiAssistantIcon from "@/components/icons/AiAssistantIcon.vue";
import DiagnosisIcon from "@/components/icons/DiagnosisIcon.vue";
import GlobeIcon from "@/components/icons/GlobeIcon.vue";
import ImageDiagnosisIcon from "@/components/icons/ImageDiagnosisIcon.vue";
import VoiceRecordingIcon from "@/components/icons/VoiceRecordingIcon.vue";
import { AREA_CODE, LANGUAGE_OPTIONS } from "@/constants/constant";
import LoginService from "@/services/loginService";
import { useCommonStore } from "@/stores/common";
import { ElMessage, ElPopover } from "element-plus";
import { storeToRefs } from "pinia";
import { computed, onMounted, ref } from "vue";
import { useI18n } from "vue-i18n";

// 全局的Pina库
const commonStore = useCommonStore();

// 全局响应数据
const { isMobile } = storeToRefs(commonStore);

// 本组件的响应数据
const { t } = useI18n();
const curAreaCode = ref(AREA_CODE[0].value);
const curPhone = ref("");
const curCaptcha = ref("");
const isAllow = ref(false);
// const rememberLogin = ref(false);
const countdown = ref(0);
const isGettingCaptcha = ref(false);
const dialogWidth = computed(() => {
  return isMobile.value ? "85%" : "55%";
});
const isPhoneValid = computed(() => {
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(curPhone.value);
});
const isCaptchaValid = computed(() => {
  const captchaRegex = /^\d{6}$/;
  return curCaptcha.value && captchaRegex.test(curCaptcha.value);
});
const isLoginDisabled = computed(() => {
  return (
    !isPhoneValid.value ||
    !isCaptchaValid.value ||
    !isAllow.value ||
    commonStore.loginLoading
  );
});
const isCaptchaDisabled = computed(() => {
  return !isPhoneValid.value || countdown.value > 0 || isGettingCaptcha.value;
});
const captchaButtonText = computed(() => {
  if (isGettingCaptcha.value) return t("loginDialog.sendingCaptcha");
  if (countdown.value > 0)
    return `${countdown.value}${t("loginDialog.resendCaptcha")}`;
  return t("loginDialog.getCaptchaButton");
});

// 本组件的方法
const startCountdown = () => {
  countdown.value = 60;
  const timer = setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      clearInterval(timer);
    }
  }, 1000);
};
const getCaptcha = async () => {
  if (!isPhoneValid.value) {
    ElMessage.error(t("loginDialog.validation.phoneRequired"));
    return;
  }

  if (!isAllow.value) {
    ElMessage.warning(t("loginDialog.validation.agreementRequired"));
    return;
  }

  try {
    isGettingCaptcha.value = true;
    await LoginService.getCaptchaFromApi(curPhone.value, curAreaCode.value);
    startCountdown();
  } catch (error) {
    console.error(t("loginDialog.validation.captchaFailed"), error);
  } finally {
    isGettingCaptcha.value = false;
  }
};
const login = async () => {
  if (!isLoginDisabled.value) {
    await commonStore.login(
      curPhone.value,
      curCaptcha.value,
      // rememberLogin.value
      true
    );
  }
};
const showLoginDialog = computed({
  get: () => commonStore.showLogin,
  set: (value: boolean) => {
    commonStore.showLogin = value;
  },
});
onMounted(() => {
  commonStore.initializeLanguage();
});
</script>

<template>
  <div class="login-dialog">
    <el-dialog
      v-model="showLoginDialog"
      :show-close="false"
      :width="dialogWidth"
      style="padding: 0; background: transparent"
    >
      <div class="login-content">
        <!-- 右上角语言切换 -->
        <div class="language-switcher">
          <el-popover
            placement="bottom-end"
            :width="200"
            popper-class="language-popover"
          >
            <template #reference>
              <el-button link>
                <GlobeIcon class="globe-icon" />
              </el-button>
            </template>
            <template #default>
              <div
                v-for="option in LANGUAGE_OPTIONS"
                :key="option.value"
                class="language-option"
                :class="{
                  active:
                    option.value === commonStore.currentLanguageInfo.value,
                }"
                @click="commonStore.handleLanguageChange(option.value)"
              >
                <span class="flag" :class="['fi', `fi-${option.flag}`]"></span>
                <span class="label">{{ option.label }}</span>
              </div>
            </template>
          </el-popover>
        </div>
        <div v-if="!isMobile" class="login-content-left">
          <div class="left-content-container">
            <div class="logo-section">
              <div class="feature-icons-left">
                <div class="floating-icon">
                  <DiagnosisIcon />
                  <span>{{ t("loginDialog.features.diagnosis") }}</span>
                </div>
                <div class="floating-icon">
                  <VoiceRecordingIcon />
                  <span>{{ t("loginDialog.features.voiceRecord") }}</span>
                </div>
              </div>

              <img src="/logo.png" alt="logo" class="centered-logo" />

              <div class="feature-icons-right">
                <div class="floating-icon">
                  <AiAssistantIcon />
                  <span>{{ t("loginDialog.features.aiAssistant") }}</span>
                </div>
                <div class="floating-icon">
                  <ImageDiagnosisIcon />
                  <span>{{ t("loginDialog.features.imageDiagnosis") }}</span>
                </div>
              </div>
            </div>

            <div class="intro-section">
              <h2 class="app-title">{{ t("loginDialog.appTitle") }}</h2>
              <p class="app-description">
                {{ t("loginDialog.appDescription") }}
              </p>
            </div>
          </div>
        </div>
        <div class="login-content-right">
          <div class="login-content-right-title">
            {{ t("loginDialog.loginTitle") }}
          </div>
          <div class="login-phone-group">
            <el-select v-model="curAreaCode" class="login-phone-select">
              <el-option
                v-for="item in AREA_CODE"
                :key="item.key"
                :label="item.value"
                :value="item.value"
              >
                <span class="login-phone-area-code">{{ item.value }}</span>
                <span class="login-phone-area-place">{{ item.label }}</span>
              </el-option>
            </el-select>
            <el-input
              v-model="curPhone"
              :placeholder="t('loginDialog.phonePlaceholder')"
              class="login-phone-input"
            />
          </div>
          <div class="login-captcha-group">
            <el-input
              class="login-captcha-input"
              v-model="curCaptcha"
              :placeholder="t('loginDialog.captchaPlaceholder')"
              maxlength="6"
              type="tel"
            />
            <button
              class="login-captcha-button"
              :class="{ disabled: isCaptchaDisabled }"
              :disabled="isCaptchaDisabled"
              @click="getCaptcha"
            >
              {{ captchaButtonText }}
            </button>
          </div>
          <div class="checkbox-group">
            <!-- <el-checkbox v-model="rememberLogin" class="remember-checkbox">
              记住登录（30天内免登录）
            </el-checkbox> -->
            <el-checkbox v-model="isAllow" class="policy-checkbox">
              {{ t("loginDialog.agreementText") }}
              <a
                href="/service-agreement"
                target="_blank"
                rel="noopener"
                class="policy-link"
                >{{ t("loginDialog.userAgreement") }}</a
              >
              {{ t("loginDialog.agreementConnector") }}
              <a
                href="/privacy-policy"
                target="_blank"
                rel="noopener"
                class="policy-link"
                >{{ t("loginDialog.privacyPolicy") }}</a
              >
            </el-checkbox>
          </div>
          <button
            class="login-button"
            :class="{ disabled: isLoginDisabled }"
            :disabled="isLoginDisabled"
            @click="login"
          >
            {{
              commonStore.loginLoading
                ? t("loginDialog.loggingIn")
                : t("loginDialog.loginButton")
            }}
          </button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<style scoped src="./LoginDialog.css"></style>
