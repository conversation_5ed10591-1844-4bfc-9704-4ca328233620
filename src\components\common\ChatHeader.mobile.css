/* 移动端样式 (767px及以下) */
@media (max-width: 767px) {
  .header-center {
    font-size: 5dvw;
  }

  .header-right {
    padding-right: 2dvw;
  }

  /* 语言切换器移动端样式 */
  .language-option {
    padding: 2.5dvw 5dvw;
  }

  .language-option .flag {
    margin-right: 2dvw;
    font-size: 5dvw;
  }

  .language-option .label {
    font-size: 3.5dvw;
    font-weight: bold;
    margin-left: 2dvw;
  }

  .globe-icon {
    animation: rotateGlobe 8s linear infinite;
    --icon-size: 6dvw;
    color: rgba(67, 170, 255);
  }

  @keyframes rotateGlobe {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }
}
