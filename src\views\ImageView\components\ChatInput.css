@import "/src/styles/components/_chat-input.css";

/* VoiceView 特有样式 */
.chat-input {
  width: calc(98% - 24px);
}

:deep(.chat-input .el-sender-action-list) {
  display: flex;
  justify-content: end;
  width: 100%;
}

.chat-input-other-button {
  font-size: 0.8dvw;
  padding: 0.2dvw 0.8dvw;
  color: rgba(51, 51, 51, 0.85);
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.7) 0%,
    rgba(255, 255, 255, 0.5) 100%
  );
  border: 1.5px solid rgba(135, 206, 235, 0.4);
  border-radius: 1dvw;
  cursor: pointer;
  white-space: nowrap;
  user-select: none;
  transition: all 0.2s ease;
  display: flex;
  justify-content: center;
  align-items: center;
  letter-spacing: 0.01dvw;
  gap: 0.15dvw;
  backdrop-filter: blur(10px);
}

.chat-input-other-button:hover {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(255, 255, 255, 0.7) 100%
  );
  border-color: rgba(135, 206, 235, 0.6);
  box-shadow: 0 2px 6px rgba(96, 151, 252, 0.3);
}

.chat-input-other-button:active {
  transform: scale(0.95);
}

.chat-input-other-button:focus-visible {
  outline: none;
  border-color: rgba(135, 206, 235, 0.8);
  box-shadow: 0 0 0 2px rgba(135, 206, 235, 0.3);
}

.chat-bubble-container {
  margin-bottom: 0.8dvw;
  display: flex;
  flex-direction: column;
  gap: 0.5dvw;
  overflow-y: auto;
  padding: 0.5dvw;
  background: linear-gradient(
    145deg,
    rgba(135, 206, 235, 0.2) 0%,
    rgba(96, 151, 252, 0.3) 50%,
    rgba(255, 105, 230, 0.2) 100%
  );
  border-radius: 1dvw;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 12px rgba(96, 151, 252, 0.15);
}
