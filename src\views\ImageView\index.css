.page-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 0.3dvw 2dvw 1dvw;
}
.page-left {
  width: 65%;
  height: 100%;
}

.chat-input.fly-chat-input {
  background: transparent;

}

.page-right {
  width: 35%;
  height: 100%;
  padding: 12px 18px 0px;
  box-sizing: border-box;
  position: relative;
  margin-left: 24px;
  background: white;
  border-radius: 0.8dvw;
  box-shadow: 3px 3px 7px 2px rgba(0, 0, 0, 0.2);
}

.page-right-title{
  margin-block: 0.3dvw 0.5dvw;
}

.page-right.welcome {
  display: flex;
  flex-direction: column;
  justify-content: center;
}
