import { FUNCTION_LIST, type FunctionItem } from "@/constants/constant";
import { ConversationType, FunctionType } from "@/constants/enums";
import ChatService from "@/services/chatService";
import type { ConversationInfo } from "@/services/conversationService";
import KnowledgeService from "@/services/knowledgeService";
import { defineStore } from "pinia";
import { ref } from "vue";
import { useRouter } from "vue-router";
import { useChatStore } from "./chat";
import { useConversationStore } from "./conversation";
import { useImagingDiagnosisStore } from "./imagingDiagnosis";
import { useMedicalRecordStore } from "./medicalRecord";
import { useVoiceStore } from "./voice";

export const useSliderStore = defineStore("slider", () => {
  const chatStore = useChatStore();
  const medicalRecordStore = useMedicalRecordStore();
  const conversationStore = useConversationStore();
  const voiceStore = useVoiceStore();
  const imagingDiagnosisStore = useImagingDiagnosisStore();
  const router = useRouter();
  const curFunction = ref(FUNCTION_LIST[0]);
  const curConversation = ref<ConversationInfo | null>(null);
  const isCollapsed = ref(false);
  const loading = ref(false);

  async function clickFunction(item: FunctionItem) {
    loading.value = true;
    ChatService.abortCurrentRequest();
    KnowledgeService.abortCurrentRequest();
    chatStore.isAiTyping = false;
    curFunction.value = item;
    curConversation.value = null;
    curFunction.value = item;
    chatStore.uploadReportImages = [];
    chatStore.uploadResultImages = [];
    chatStore.messageList = [];
    voiceStore.recognitionText = "";
    curConversation.value = null;
    medicalRecordStore.medicalRecord = undefined;
    if (item.id === FunctionType.CHAT) {
      await conversationStore.getConversationList(
        1,
        ConversationType.DIAGNOSIS
      );
    } else if (item.id === FunctionType.VOICE) {
      await conversationStore.getConversationList(1, ConversationType.VOICE);
    } else if (item.id === FunctionType.KNOWLEDGE) {
      await conversationStore.getConversationList(
        1,
        ConversationType.KNOWLEDGE
      );
    } else if (item.id === FunctionType.IMAGE) {
      await conversationStore.getConversationList(1, ConversationType.IMAGE);
    }
    router.push(`/${item.id}`);
    loading.value = false;
  }

  async function clickHistory(item: ConversationInfo) {
    loading.value = true;
    chatStore.isAiTyping = false;
    curConversation.value = item;
    chatStore.uploadReportImages = [];
    chatStore.uploadResultImages = [];
    if (curFunction.value.id === FunctionType.KNOWLEDGE) {
      await chatStore.getKnowledgeMessageList(item.conversation_id);
    } else if (curFunction.value.id === FunctionType.IMAGE) {
      const xRayRecords = await imagingDiagnosisStore.getXRayRecords(item.id);
      if (xRayRecords?.image_files && xRayRecords.image_files.length > 0) {
        imagingDiagnosisStore.imageFiles = xRayRecords.image_files.map(
          (file) => {
            return {
              uid: file.file_id,
              name: file.cos_path.split("/").filter(Boolean).pop() ?? "",
              size: file.size,
              status: "success",
              percentage: 100,
              raw: "File",
              response: {
                data: file,
              },
            };
          }
        ) as any;
      } else {
        imagingDiagnosisStore.imageFiles = [];
      }
      imagingDiagnosisStore.analyzeResult = xRayRecords?.analyze_result ?? "";
      await chatStore.getMessageList(item.conversation_id);
    } else {
      await chatStore.getMessageList(item.conversation_id);
    }
    if (item.voice_medical_records_id) {
      const result = await medicalRecordStore.getVoiceMedicalRecord(
        item.voice_medical_records_id
      );
      medicalRecordStore.medicalRecord = result;
    }
    loading.value = false;
  }

  // 创建新对话
  const clickNewChat = () => {
    chatStore.isAiTyping = false;
    curConversation.value = null;
    chatStore.uploadReportImages = [];
    chatStore.uploadResultImages = [];
    chatStore.messageList = [];
    medicalRecordStore.medicalRecord = undefined;
  };

  return {
    loading,
    curFunction,
    curConversation,
    isCollapsed,
    clickFunction,
    clickHistory,
    clickNewChat,
  };
});
