import api from "@/utils/api";

// 病历记录基础模型接口
export interface XRayRecords {
  // 基本信息
  conversation_id: number;
  image_ids?: number[];
  analyze_result?: string;
}

// 影像报告查询响应数据结构
export interface GetXRayRecordsResponse {
  id: number;
  conversation_id: number;
  image_ids: number[];
  analyze_result: string;
  created_at: string;
  updated_at: string;
  created_by_id: number;
  updated_by_id: number;
  created_by: string;
  updated_by: string;
  is_deleted: boolean;
  deleted_at: string | null;
  image_files: {
    file_id: number;
    cos_path: string;
    cos_type: string;
    size: number;
    url: string;
    content_type: string;
    text_content: string | null;
    created_at: string;
    created_by: number;
  }[];
}

/**
 * 影像诊断服务类
 */
export class ImagingDiagnosisService {
  /**
   * 影像诊断分析
   *
   */
  static async imageAnalyzeFromApi(images: { url: string }[]): Promise<any> {
    return await api.post<any>("/api/xray-analysis/analyze", {
      images: images,
    });
  }

  /**
   * 更新或新增影像报告
   *
   */
  static async saveOrUpdateXRayRecordsFromApi(
    xRayRecords: XRayRecords
  ): Promise<any> {
    return await api.post<any>("/api/x-ray-records/", xRayRecords);
  }

  /**
   * 查询影像报告
   *
   */
  static async getXRayRecordsFromApi(id: number): Promise<GetXRayRecordsResponse> {
    return await api.get<GetXRayRecordsResponse>(`/api/x-ray-records/conversation/${id}`);
  }
}

export default ImagingDiagnosisService;