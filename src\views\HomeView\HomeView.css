.home-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  overflow: auto;
}

.home-one {
  width: 100%;
  position: relative;
}

.home-one-bg {
  width: 100%;
}

/* 粒子系统 */
.home-one-particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  pointer-events: none;
}

.particle {
  position: absolute;
  background: rgba(42, 141, 255, 0.6);
  border-radius: 50%;
  animation: floatUp linear infinite;
  box-shadow: 0 0 10px rgba(42, 141, 255, 0.3);
}

.particle:nth-child(even) {
  background: rgba(255, 255, 255, 0.4);
  box-shadow: 0 0 8px rgba(255, 255, 255, 0.2);
}

.home-one-text {
  position: absolute;
  top: 10%;
  left: 6%;
  z-index: 10;
}

.home-one-text-title {
  font-size: 4dvw;
  color: var(--home-button-color);
  font-weight: bold;
  margin-bottom: 3dvh;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  position: relative;
}

.home-one-text-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--home-button-color), rgba(42, 141, 255, 0.3));
  animation: underlineExpand 2s ease-out 1s forwards;
}

.home-one-text-subtitle {
  font-size: 2dvw;
  font-weight: bold;
  margin-bottom: 5dvh;
  color: #333;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

.home-one-text-desc {
  font-size: 1dvw;
  color: var(--home-color-gray-primary);
}

.home-one-text-desc-p {
  font-size: 1dvw;
  color: var(--home-color-gray-primary);
  margin-block: 2dvh;
}

.home-one-text-button {
  position: relative;
  padding: 1.5dvw 3dvw;
  font-size: 1.1dvw;
  color: white;
  cursor: pointer;
  background: linear-gradient(135deg, var(--home-button-color), #1976d2);
  border: none;
  border-radius: 50px;
  margin-block: 4dvh 10dvh;
  overflow: hidden;
  box-shadow: 
    0 4px 15px rgba(42, 141, 255, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(0);
}

.home-one-text-button:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 8px 25px rgba(42, 141, 255, 0.6),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  background: linear-gradient(135deg, #1976d2, var(--home-button-color));
}

.home-one-text-button:active {
  transform: translateY(0);
  box-shadow: 
    0 2px 10px rgba(42, 141, 255, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.button-text {
  position: relative;
  z-index: 2;
  font-weight: 600;
}

.button-glow {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.6s;
}

.home-one-text-button:hover .button-glow {
  left: 100%;
}

.home-two {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 85dvh;
  position: relative;
  overflow: hidden;
  padding-inline: auto;
  width: 100%;
  background:
    radial-gradient(ellipse at center, rgba(42, 141, 255, 0.03) 0%, transparent 70%),
    linear-gradient(135deg, rgba(42, 141, 255, 0.02) 0%, transparent 50%, rgba(61, 105, 238, 0.02) 100%);
}

/* 全息边框效果 */
.home-two::before {
  content: '';
  position: absolute;
  top: 5%;
  left: 5%;
  right: 5%;
  bottom: 5%;
  border: 1px solid rgba(42, 141, 255, 0.2);
  border-radius: 20px;
  background: rgba(42, 141, 255, 0.01);
  backdrop-filter: blur(1px);
  animation: hologramBorder 10s ease-in-out infinite;
  z-index: 1;
}

/* 科技感背景动画 */
.home-two-particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
  overflow: hidden;
}

/* 网格背景 */
.home-two-particles::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(42, 141, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(42, 141, 255, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: gridMove 20s linear infinite;
}

/* 扫描线效果 */
.home-two-particles::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(42, 141, 255, 0.1),
    rgba(42, 141, 255, 0.3),
    rgba(42, 141, 255, 0.1),
    transparent
  );
  animation: scanLine 8s ease-in-out infinite;
}

/* 科技几何图形容器 */
.tech-geometry {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

/* 科技圆环 */
.tech-circle {
  position: absolute;
  border: 2px solid rgba(42, 141, 255, 0.2);
  border-radius: 50%;
  animation: techRotate 15s linear infinite;
}

.tech-circle-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  right: 15%;
  border-style: dashed;
  animation-duration: 20s;
}

.tech-circle-2 {
  width: 150px;
  height: 150px;
  bottom: 20%;
  left: 10%;
  border-color: rgba(61, 105, 238, 0.3);
  animation-direction: reverse;
  animation-duration: 25s;
}

/* 科技六边形 */
.tech-hexagon {
  position: absolute;
  width: 80px;
  height: 80px;
  background: rgba(42, 141, 255, 0.05);
  border: 1px solid rgba(42, 141, 255, 0.2);
  clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
  animation: techFloat 12s ease-in-out infinite;
}

.tech-hexagon-1 {
  top: 15%;
  left: 20%;
  animation-delay: 0s;
}

.tech-hexagon-2 {
  bottom: 15%;
  right: 25%;
  animation-delay: 6s;
  transform: scale(1.2);
}

/* 科技连接线 */
.tech-line {
  position: absolute;
  background: linear-gradient(90deg, transparent, rgba(42, 141, 255, 0.4), transparent);
  height: 1px;
  animation: techPulse 8s ease-in-out infinite;
}

.tech-line-1 {
  width: 200px;
  top: 25%;
  left: 30%;
  transform: rotate(45deg);
  animation-delay: 0s;
}

.tech-line-2 {
  width: 150px;
  top: 60%;
  right: 20%;
  transform: rotate(-30deg);
  animation-delay: 2s;
}

.tech-line-3 {
  width: 180px;
  bottom: 30%;
  left: 15%;
  transform: rotate(15deg);
  animation-delay: 4s;
}

/* 数据流光点 */
.data-stream {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.data-dot {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(42, 141, 255, 0.8);
  border-radius: 50%;
  box-shadow: 0 0 10px rgba(42, 141, 255, 0.6);
  animation: dataFlow 6s linear infinite;
}

.data-dot-1 {
  top: 20%;
  left: 0;
  animation-delay: 0s;
}

.data-dot-2 {
  top: 40%;
  left: 0;
  animation-delay: 1.5s;
}

.data-dot-3 {
  top: 60%;
  left: 0;
  animation-delay: 3s;
}

.data-dot-4 {
  top: 80%;
  left: 0;
  animation-delay: 4.5s;
}

.home-two-text {
  position: relative;
  z-index: 2;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s ease-out;
}

.home-two-text.animate-title {
  opacity: 1;
  transform: translateY(0);
}

.home-two-text-title {
  font-size: 2dvw;
  font-weight: bold;
  margin-bottom: 3dvh;
  text-align: center;
  position: relative;
}

.home-two-text-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--home-button-color), rgba(42, 141, 255, 0.3));
  transition: width 0.8s ease-out 0.5s;
}

.home-two-text.animate-title .home-two-text-title::after {
  width: 60px;
}

.home-two-text-desc {
  font-size: 1dvw;
  color: var(--home-color-gray-primary);
  margin-bottom: 5dvw;
}

.home-two-list {
  display: flex;
  gap: 2dvw;
  position: relative;
  z-index: 2;
}

.home-two-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: var(--home-color-blue-light);
  width: 14dvw;
  padding: 2dvw 2dvw;
  border-radius: 1dvw;
  opacity: 0;
  transform: translateY(50px) scale(0.9);
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(42, 141, 255, 0.1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  cursor: pointer;
}

.home-two-box::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(42, 141, 255, 0.1), transparent);
  transition: left 0.6s ease;
}

.home-two-box.animate-box {
  opacity: 1;
  transform: translateY(0) scale(1);
}

.home-two-box.animate-box::before {
  left: 100%;
}

.home-two-box:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow:
    0 8px 30px rgba(42, 141, 255, 0.15),
    0 0 20px rgba(42, 141, 255, 0.1);
  border-color: rgba(42, 141, 255, 0.2);
}

.home-two-box-icon {
  position: relative;
  z-index: 2;
}

.home-two-box-icon-svg {
  padding: 1dvw;
  background: var(--home-color-blue-dark);
  border-radius: 50%;
  margin-bottom: 2dvw;
  color: var(--home-button-color);
  font-size: 2.5dvw;
  position: relative;
  transition: all 0.3s ease;
}

/* 图标浮动动画 */
.floating-icon {
  animation: iconFloat 3s ease-in-out infinite;
}

.floating-icon:nth-child(1) {
  animation-delay: 0s;
}

.floating-icon:nth-child(2) {
  animation-delay: 0.5s;
}

.floating-icon:nth-child(3) {
  animation-delay: 1s;
}

.floating-icon:nth-child(4) {
  animation-delay: 1.5s;
}

.home-two-box:hover .home-two-box-icon-svg {
  transform: scale(1.1);
  background: var(--home-button-color);
  color: white;
  box-shadow: 0 0 20px rgba(42, 141, 255, 0.4);
}

/* 科技感发光效果 */
.home-two-box.animate-box {
  animation: boxGlow 4s ease-in-out infinite;
}

.home-two-box.animate-box:nth-child(1) {
  animation-delay: 0s;
}

.home-two-box.animate-box:nth-child(2) {
  animation-delay: 1s;
}

.home-two-box.animate-box:nth-child(3) {
  animation-delay: 2s;
}

.home-two-box.animate-box:nth-child(4) {
  animation-delay: 3s;
}

.home-two-box-title {
  font-size: 1.2dvw;
  font-weight: bold;
  margin-bottom: 2dvw;
}

.home-two-box-desc {
  font-size: 1dvw;
  font-weight: bold;
  color: var(--home-color-gray-primary);
  text-align: center;
}

.home-three {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 85dvh;
  background: var(--home-color-blue-light);
  width: 100%;
}

.home-three-text-title {
  font-size: 2dvw;
  font-weight: bold;
  margin-bottom: 3dvh;
  text-align: center;
}

.home-three-carousel {
  width: 83%;
  margin-top: 5dvh;
}

.home-three-carousel-item {
  display: flex;
  padding-inline: 4%;
  gap: 2dvw;
  height: 50dvh;
}

.home-three-carousel-item-left {
  width: 50%;
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: center;
}
.home-three-carousel-item-title {
  font-size: 1.5dvw;
  font-weight: bold;
}

.home-three-carousel-item-desc {
  font-size: 0.95dvw;
  padding-block: 2dvw;
  color: var(--home-color-gray-primary);
  letter-spacing: 0.02dvw;
  font-weight: bold;
  line-height: 1.6dvw;
}

.home-three-carousel-item-button-list {
  display: flex;
  gap: 2dvw;
}

.home-three-carousel-item-button {
  display: flex;
  gap: 2dvw;
  color: var(--home-color-blue-primary);
  background: var(--home-color-blue-dark);
  padding: 0.6dvw 1.4dvw;
  border-radius: 1.5dvw;
  font-size: 0.95dvw;
  font-weight: bold;
}

.home-three-carousel-item-right {
  width: 50%;
  display: flex;
  align-items: center;
}

.home-three-carousel-item-img {
  width: 100%;
}

.home-four {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 85dvh;
  width: 100%;
}

.home-four-text-title {
  font-size: 2dvw;
  font-weight: bold;
  text-align: center;
}

.home-four-carousel {
  width: 83%;
  margin-top: 5dvh;
}

.home-four-carousel-item {
  display: flex;
  padding-inline: 4%;
  gap: 2dvw;
  height: 40dvh;
  align-items: center;
}

.home-four-carousel-item-left,
.home-four-carousel-item-right {
  width: 44%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  background: var(--home-color-blue-light);
  border-radius: 1.2dvw;
  padding-inline: 3%;
  padding-block: 4%;
}

.home-four-carousel-item-doctor {
  display: flex;
  align-items: center;
  gap: 1dvw;
  margin-bottom: 4dvh;
}

.home-four-carousel-item-doctor-img {
  height: 8dvh;
  border-radius: 50%;
}

.home-four-carousel-item-doctor-name {
  font-size: 1.1dvw;
  font-weight: bold;
  margin-bottom: 0.5dvw;
}

.home-four-carousel-item-doctor-company {
  color: var(--home-color-gray-primary);
  letter-spacing: 0.03dvw;
  font-size: 0.8dvw;
}

.home-four-carousel-item-desc {
  color: var(--home-color-gray-dark);
  letter-spacing: 0.01dvw;
  font-size: 0.85dvw;
  line-height: 1.3dvw;
  margin-bottom: 2dvh;
}

.home-four-carousel-item-star {
  display: flex;
  gap: 0.3dvw;
}

.home-footer {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  width: 80%;
  gap: 5dvw;
  border-top: 3px solid transparent;
  padding: 2% 10% 1%;
  background: var(--home-footer-background);
  color: var(--home-color-white);
}

.home-footer-desc {
  color: var(--home-color-white-dark);
  font-size: 0.7dvw;
  line-height: 1.2dvw;
}
.contact-information {
  display: flex;
  align-items: center;
  gap: 0.5dvw;
  margin-bottom: 0.3dvw;
}

.home-footer-desc-icon {
  font-size: 1dvw;
}

.home-footer-item-qrcode-list {
  display: flex;
}

.home-footer-item-qrcode {
  flex: 1;
}

.home-footer-item-image {
  width: 80%;
}

.home-footer-item-image-popover {
  width: 100%;
}
.home-footer-item-title {
  text-align: center;
  width: 80%;
  font-size: 0.7dvw;
  margin-top: 0.3dvw
}

.home-registration {
  width: 100%;
  display: flex;
  justify-content: center;
  color: var(--home-color-white-dark);
  background: var(--home-footer-background);
  position: relative;
}

.home-registration::before {
  content: '';
  position: absolute;
  top: 0;
  left: 10%;
  right: 10%;
  height: 1px;
  background-color: var(--home-color-white-dark);
  transform: scaleY(0.5);
}

.home-registration-text {
  padding: 3dvh 4dvw 6dvh;
  font-size: 0.7dvw;
}

.home-text-pointer{
  cursor: pointer;
  margin-bottom: 0.3dvw;
}

/* ===== 动画效果 ===== */

/* 淡入向上动画 */
.opacity-in {
  animation: opacityIn 1s ease-out 0.8s both;
}

.bg-opacity-in {
  animation: opacityIn 0.6s ease-out 0.3s both;
}

@keyframes opacityIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

/* 背景缓慢缩放动画 */
@keyframes slowZoom {
  0% { transform: scale(1); }
  100% { transform: scale(1.1); }
}

/* 粒子上浮动画 */
@keyframes floatUp {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-20vh) rotate(360deg);
    opacity: 0;
  }
}

/* 下划线扩展动画 */
@keyframes underlineExpand {
  0% { width: 0; }
  100% { width: 60%; }
}

/* 脉冲按钮效果 */
.pulse-button {
  animation: pulseGlow 2s ease-in-out infinite;
}

@keyframes pulseGlow {
  0%, 100% {
    box-shadow: 
      0 4px 15px rgba(42, 141, 255, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }
  50% {
    box-shadow: 
      0 4px 20px rgba(42, 141, 255, 0.6),
      inset 0 1px 0 rgba(255, 255, 255, 0.3),
      0 0 30px rgba(42, 141, 255, 0.3);
  }
}

/* 淡入向上动画 */
.fade-in-up {
  animation: fadeInUp 1s ease-out 0.8s both;
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 从右侧滑入 */
.slide-in-right {
  animation: slideInRight 1s ease-out 1.5s both;
}

@keyframes slideInRight {
  0% {
    opacity: 0;
    transform: translateX(50px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 从左侧滑入（带延迟） */
.slide-in-left {
  animation: slideInLeft 0.8s ease-out both;
}

.slide-in-left.delay-1 {
  animation-delay: 1.7s;
}

.slide-in-left.delay-2 {
  animation-delay: 2s;
}

.slide-in-left.delay-3 {
  animation-delay: 2.3s;
}

@keyframes slideInLeft {
  0% {
    opacity: 0;
    transform: translateX(-30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 缩放进入 */
.scale-in {
  animation: scaleIn 0.6s ease-out 2.8s both;
}

@keyframes scaleIn {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* ===== Home-Two 动画效果 ===== */

/* 网格移动动画 */
@keyframes gridMove {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(50px, 50px);
  }
}

/* 扫描线动画 */
@keyframes scanLine {
  0% {
    left: -100%;
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    left: 100%;
    opacity: 0;
  }
}

/* 科技旋转动画 */
@keyframes techRotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 科技浮动动画 */
@keyframes techFloat {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-15px) rotate(180deg);
    opacity: 1;
  }
}

/* 科技脉冲动画 */
@keyframes techPulse {
  0%, 100% {
    opacity: 0.2;
    transform: scaleX(0.8);
  }
  50% {
    opacity: 0.8;
    transform: scaleX(1.2);
  }
}

/* 数据流动画 */
@keyframes dataFlow {
  0% {
    left: -10px;
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    left: calc(100% + 10px);
    opacity: 0;
  }
}

/* 全息边框动画 */
@keyframes hologramBorder {
  0%, 100% {
    border-color: rgba(42, 141, 255, 0.2);
    box-shadow:
      inset 0 0 20px rgba(42, 141, 255, 0.1),
      0 0 20px rgba(42, 141, 255, 0.1);
  }
  50% {
    border-color: rgba(42, 141, 255, 0.4);
    box-shadow:
      inset 0 0 30px rgba(42, 141, 255, 0.2),
      0 0 30px rgba(42, 141, 255, 0.2);
  }
}

/* 图标上下浮动 */
@keyframes iconFloat {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-8px);
  }
}

/* 盒子发光效果 */
@keyframes boxGlow {
  0%, 100% {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  }
  50% {
    box-shadow:
      0 4px 20px rgba(0, 0, 0, 0.05),
      0 0 30px rgba(42, 141, 255, 0.1),
      inset 0 0 20px rgba(42, 141, 255, 0.05);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .home-one-text {
    top: 15%;
    left: 4%;
    right: 4%;
  }

  .home-one-text-title {
    font-size: 6dvw;
  }

  .home-one-text-subtitle {
    font-size: 3.5dvw;
  }

  .home-one-text-desc-p {
    font-size: 2.5dvw;
  }

  .home-one-text-button {
    padding: 3dvw 6dvw;
    font-size: 2.5dvw;
  }

  .particle {
    animation-duration: 8s !important;
  }

  /* Home-Two 移动端适配 */
  .home-two {
    height: auto;
    padding: 10dvh 0;
  }

  .home-two-text-title {
    font-size: 5dvw;
  }

  .home-two-text-desc {
    font-size: 3dvw;
    margin-bottom: 8dvw;
    padding: 0 4dvw;
    text-align: center;
  }

  .home-two-list {
    flex-direction: column;
    gap: 4dvw;
    padding: 0 4dvw;
  }

  .home-two-box {
    width: 100%;
    padding: 4dvw 3dvw;
  }

  .home-two-box-icon-svg {
    font-size: 6dvw;
    padding: 2dvw;
  }

  .home-two-box-title {
    font-size: 3.5dvw;
  }

  .home-two-box-desc {
    font-size: 2.8dvw;
    line-height: 1.5;
  }

  /* 移动端简化科技背景效果 */
  .tech-circle {
    display: none;
  }

  .tech-hexagon {
    width: 40px;
    height: 40px;
  }

  .tech-line {
    width: 100px !important;
  }

  /* 调整移动端动画强度 */
  .home-two-box:hover {
    transform: translateY(-2px) scale(1.01);
  }

  @keyframes iconFloat {
    0%, 100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-4px);
    }
  }
}
