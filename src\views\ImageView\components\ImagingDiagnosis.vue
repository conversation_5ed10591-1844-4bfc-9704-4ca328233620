<script lang="ts" setup>
import AutoReportIcon from "@/components/icons/AutoReportIcon.vue";
import ImageAnalysisIcon from "@/components/icons/ImageAnalysisIcon.vue";
import XrayIcon from "@/components/icons/XrayIcon.vue";
import { ConversationType } from "@/constants/enums";
import { useConversationStore } from "@/stores/conversation";
import { useImagingDiagnosisStore } from "@/stores/imagingDiagnosis";
import { useSliderStore } from "@/stores/slider";
import { TokenCookieManager } from "@/utils/cookieUtils";
import { ElMessage, type UploadUserFile } from "element-plus";
import { debounce } from "lodash-es";
import { storeToRefs } from "pinia";
import { computed, onUnmounted, ref } from "vue";
// 全局Pina库
const imagingDiagnosisStore = useImagingDiagnosisStore();
const sliderStore = useSliderStore();
const conversationStore = useConversationStore();

// 全局响应数据
const { imageFiles, analyzeResult } = storeToRefs(imagingDiagnosisStore);
const { curConversation } = storeToRefs(sliderStore);

// 全局方法
const { imageAnalyze, saveOrUpdateXRayRecords } = imagingDiagnosisStore;
const { addConversation } = conversationStore;

// 本组件的响应数据
const uploadLoading = ref(false);
const analyzeLoading = ref(false);
const previewImageFiles = computed(() => {
  return imageFiles.value.map((item: any) => {
    let url = "";
    if (item?.response?.data?.url) {
      url = item.response.data.url;
    }
    return {
      ...item,
      url: url,
    };
  });
});
const buttonDisabled = computed(() => {
  return imageFiles.value.length === 0 || previewImageFiles.value.some(item => !item.url)
});

// 本组件的方法
const handleImageRemove = (imageFile: UploadUserFile) => {
  imageFiles.value = imageFiles.value.filter(
    (item) => item.uid !== imageFile.uid
  );
};

const handleExceed = (_files: File[], _uploadFiles: UploadUserFile[]) => {
  ElMessage.warning(`最多允许上传 4 张图片`);
};

const handleAnalyzeImage = async () => {
  let curConversationId
  analyzeLoading.value = true;
  const images = previewImageFiles.value.map(item => ({ url: item.url ?? "" }))
  const result = await imageAnalyze(images)
  analyzeResult.value = result.content
  if (!!curConversation.value) {
    curConversationId = curConversation.value.id;
  } else {
    const newConversation = await addConversation(
      result.content.slice(0, 20),
      ConversationType.IMAGE
    );
    curConversationId = newConversation.id;
    curConversation.value = newConversation;
  }
  await saveOrUpdateXRayRecords({
    conversation_id: curConversationId,
    image_ids: imageFiles.value.map((item: any) => item.response.data.file_id),
    analyze_result: result.content
  })
  analyzeLoading.value = false;
};

const handleAutoSave = debounce(async (): Promise<void> => {
  if (analyzeResult.value) {
    analyzeLoading.value = true;
    let curConversationId
    if (!!curConversation.value) {
      curConversationId = curConversation.value.id;
    } else {
      const newConversation = await addConversation(
        analyzeResult.value.slice(0, 20),
        ConversationType.IMAGE
      );
      curConversationId = newConversation.id;
      curConversation.value = newConversation;
    }
    await saveOrUpdateXRayRecords({
      conversation_id: curConversationId,
      image_ids: imageFiles.value.map((item: any) => item.response.data.file_id),
      analyze_result: analyzeResult.value
    })
    analyzeLoading.value = false;
  }
}, 1500);

onUnmounted(() => {
  handleAutoSave.cancel();
});
</script>

<template>
  <div class="imaging-diagnosis">
    <div class="content-container">
      <div v-if="imageFiles.length === 0" class="no-image-show-card">
        <img class="no-image-show-img" src="/image/imaging_diagnosis.png" />
        <div>
          <p class="no-image-show-text">
            <XrayIcon class="icon" />X-ray高精度影像识别
          </p>
          <p class="no-image-show-text">
            <ImageAnalysisIcon class="icon" />深度影像特征解析
          </p>
          <p class="no-image-show-text">
            <AutoReportIcon class="icon" />全自动报告生成
          </p>
        </div>
      </div>
      <div v-else class="image-show-card">
        <div class="left-container">
          <el-image class="main-image" :src="previewImageFiles[0].url"
            :preview-src-list="previewImageFiles.map((item) => item.url)" show-progress :initial-index="4" fit="cover">
            <template #error>
              <el-skeleton :loading="true" animated variant="image" class="no-main-image">
                <template #template>
                  <el-skeleton-item variant="image" class="no-main-image" />
                </template>
              </el-skeleton>
            </template>
          </el-image>
          <div class="upload-file-image-options">
            <el-button class="delete-button" size="small" type="danger" circle plain
              @click.stop="handleImageRemove(previewImageFiles[0])">
              <el-icon>
                <delete />
              </el-icon>
            </el-button>
          </div>
        </div>
        <div class="right-container">
          <el-image v-for="(imageItem, index) in previewImageFiles.slice(1)" class="secondary-image"
            :class="`num-${index + 1}`" :src="imageItem.url"
            :preview-src-list="previewImageFiles.map((item) => item.url)" show-progress :initial-index="index + 1"
            fit="cover">
            <template #error>
              <el-skeleton :loading="true" animated variant="image" class="no-main-image">
                <template #template>
                  <el-skeleton-item variant="image" class="no-main-image" />
                </template>
              </el-skeleton>
            </template>
          </el-image>
          <div v-for="(imageItem, index) in previewImageFiles.slice(1)" class="upload-file-image-options"
            :class="`num-${index + 1}`">
            <el-button class="delete-button" size="small" type="danger" circle plain
              @click.stop="handleImageRemove(imageItem)">
              <el-icon>
                <delete />
              </el-icon>
            </el-button>
          </div>
        </div>
      </div>
      <div class="image-upload-card">
        <el-upload class="upload-dragger" drag multiple v-model:file-list="imageFiles" :disabled="uploadLoading"
          :show-file-list="false" action="/api/xray-analysis/upload_image" :headers="{
            Authorization: 'Bearer ' + TokenCookieManager.getToken(),
          }" :limit="4" :on-exceed="handleExceed">
          <span class="el-upload__text">
            支持点击上传、截图粘贴、拖拽方式上传图像，
            支持PNG、JPG、JPEG格式，最多可上传4个文件
          </span>
        </el-upload>
      </div>
    </div>
    <div class="footer-container">
      <h3 class="title">影像报告</h3>
      <div class="text-container">
        <el-input :readonly="buttonDisabled" class="text-area" v-model="analyzeResult" maxlength="500"
          placeholder="请上传 X-Ray 影像后，点击“开始分析”即可查看分析结果" type="textarea" :autosize="{ minRows: 2, maxRows: 4 }"
          @blur="handleAutoSave" @keyup.enter="handleAutoSave" />
      </div>
      <div class="button-container">
        <el-button :loading="analyzeLoading" type="primary" :disabled="buttonDisabled"
          @click="handleAnalyzeImage">开始分析</el-button>
      </div>
    </div>
  </div>
</template>
<style scoped src="./ImagingDiagnosis.css"></style>
